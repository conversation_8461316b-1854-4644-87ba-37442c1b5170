<?php

declare(strict_types=1);

namespace App\EventListener;

use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Core\Security;

class CreatorAccessListener
{
    private $security;
    private $router;

    public function __construct(Security $security, RouterInterface $router)
    {
        $this->security = $security;
        $this->router = $router;
    }

    public function onKernelRequest(RequestEvent $event)
    {
        if (!$event->isMainRequest()) {
            return;
        }

        $request = $event->getRequest();
        $path = $request->getPathInfo();

        if (!$this->security->isGranted('ROLE_CREATOR') || !str_starts_with($path, '/admin')) {
            return;
        }

        $allowedRoutes = [
            '/admin/courses',
            '/admin/campus',
            '/admin',
        ];

        $isAllowed = false;
        foreach ($allowedRoutes as $allowedRoute) {
            if (str_starts_with($path, $allowedRoute)) {
                $isAllowed = true;
                break;
            }
        }

        if (!$isAllowed) {
            throw new AccessDeniedException('No tienes permiso para acceder a esta sección');
        }
    }
}
