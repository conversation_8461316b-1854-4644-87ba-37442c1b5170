<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Itineraries;

use App\Entity\Course;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminItinerariesEndpoint;
use App\Tests\Functional\HelperTrait\ItineraryHelperTrait;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;

class GetItineraryCoursesFunctionalTest extends FunctionalTestCase
{
    use ItineraryHelperTrait;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function setUp(): void
    {
        parent::setUp();

        $this->creatorUser = $this->createAndGetUser(
            firstName: 'Creator',
            lastName: 'User',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>',
        );
        $this->defaultUserRoles = $this->getDefaultUser()->getRoles();
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    #[DataProvider('itineraryCoursesDataProvider')]
    public function testGetCoursesForItinerary(array $roles, int $expectedCourses)
    {
        $user = $this->getDefaultUser()->setRoles($roles);
        $this->getEntityManager()->flush();

        $course1 = $this->createAndGetCourse(
            name: 'Course 1',
            createdBy: $this->creatorUser,
        );
        $course2 = $this->createAndGetCourse(
            name: 'Course 2',
        );
        $course3 = $this->createAndGetCourse(
            name: 'Course 3',
        );
        $course3->setManagers([$user]);

        $userToken = $this->loginAndGetToken();
        $response = $this->makeAdminApiRequest(
            'GET',
            AdminItinerariesEndpoint::itinerariesFindCoursesEndpoint(),
            [],
            [],
            [],
            $userToken
        );
        $this->assertEquals(200, $response->getStatusCode());
        $data = $this->extractResponseData($response);
        $this->assertCount($expectedCourses, $data);
    }

    public static function itineraryCoursesDataProvider(): \Generator
    {
        yield 'Admin user can see all courses' => [
            'roles' => ['ROLE_ADMIN'],
            'expectedCourses' => 3,
        ];

        yield 'Only manager role. Can see only shared courses' => [
            'roles' => ['ROLE_MANAGER'],
            'expectedCourses' => 1,
        ];

        yield 'Admin & manager role. Can see all courses' => [
            'roles' => ['ROLE_ADMIN', 'ROLE_MANAGER'],
            'expectedCourses' => 3,
        ];
    }

    /**
     * @throws Exception
     * @throws MappingException
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function tearDown(): void
    {
        $this->getDefaultUser()->setRoles($this->defaultUserRoles);
        $this->getEntityManager()->flush();

        $this->truncateEntities([
            Course::class,
        ]);

        $usersIds = array_filter([
            $this->creatorUser?->getId(),
        ]);

        if (!empty($usersIds)) {
            $this->hardDeleteUsersByIds($usersIds);
        }

        parent::tearDown();
    }
}
