<?php

namespace App\Tests\Functional\Listener;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementManager;
use App\Entity\AnnouncementTutor;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\UserLogin;
use App\Entity\UserManage;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminPurchaseEndpoints;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\PurchasableItemEndpoints;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Symfony\Component\HttpFoundation\Response;

class CreatorAccessListenerFunctionalTest extends FunctionalTestCase
{
    private const string EMAIL = '<EMAIL>';

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws Exception
     */
    public function testCreatorAccessListenerIsDeleted(): void
    {
        $user = $this->createAndGetUser(
            roles: ['ROLE_USER', 'ROLE_CREATOR', 'ROLE_ADMIN'],
            email: self::EMAIL,
        );

        $response = $this->makeRequest(
            method: 'GET',
            uri: PurchasableItemEndpoints::getPurchasableItemsEndpoint(),
            bearerToken: $this->loginAndGetTokenForUser($user),
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $this->hardDeleteUsersByIds([
            $user->getId(),
        ]);
    }
}